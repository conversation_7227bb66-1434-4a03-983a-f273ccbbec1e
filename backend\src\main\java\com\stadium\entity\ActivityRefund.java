package com.stadium.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 活动退款实体类
 */
@Data
@TableName("activity_refund")
@Schema(description = "活动退款实体")
public class ActivityRefund {

    @TableId(type = IdType.AUTO)
    @Schema(description = "退款ID")
    private Long id;

    @Schema(description = "活动ID")
    @TableField("activity_id")
    private Long activityId;

    @Schema(description = "用户ID")
    @TableField("user_id")
    private Long userId;

    @Schema(description = "报名ID")
    @TableField("registration_id")
    private Long registrationId;

    @Schema(description = "退款金额")
    @TableField("amount")
    private BigDecimal amount;

    @Schema(description = "退款原因")
    @TableField("reason")
    private String reason;

    @Schema(description = "状态：0-待审核，1-已通过，2-已拒绝，3-已退款")
    @TableField("status")
    private Integer status;

    @Schema(description = "审核备注")
    @TableField("review_remark")
    private String reviewRemark;

    @Schema(description = "审核人ID")
    @TableField("reviewer_id")
    private Long reviewerId;

    @Schema(description = "审核时间")
    @TableField("review_time")
    private LocalDateTime reviewTime;

    @Schema(description = "退款时间")
    @TableField("refund_time")
    private LocalDateTime refundTime;

    /**
     * 退款状态：待审核
     */
    public static final int STATUS_PENDING = 0;

    /**
     * 退款状态：已通过审核
     */
    public static final int STATUS_APPROVED = 1;

    /**
     * 退款状态：已拒绝
     */
    public static final int STATUS_REJECTED = 2;

    /**
     * 退款状态：已退款
     */
    public static final int STATUS_REFUNDED = 3;
}