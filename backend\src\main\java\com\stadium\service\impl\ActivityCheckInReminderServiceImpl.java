package com.stadium.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.stadium.dto.ActivityCheckInReminderDTO;
import com.stadium.entity.Activity;
import com.stadium.entity.ActivityCheckInReminder;
import com.stadium.entity.ActivityRegistration;
import com.stadium.entity.User;
import com.stadium.mapper.ActivityCheckInReminderMapper;
import com.stadium.mapper.ActivityMapper;
import com.stadium.mapper.ActivityRegistrationMapper;
import com.stadium.mapper.UserMapper;
import com.stadium.service.ActivityCheckInReminderService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动签到提醒服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ActivityCheckInReminderServiceImpl
        extends ServiceImpl<ActivityCheckInReminderMapper, ActivityCheckInReminder>
        implements ActivityCheckInReminderService {

    private final ActivityMapper activityMapper;
    private final ActivityRegistrationMapper registrationMapper;
    private final UserMapper userMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long sendPreActivityReminder(Long activityId) {
        // 获取活动信息
        Activity activity = activityMapper.selectById(activityId);
        if (activity == null) {
            throw new RuntimeException("活动不存在");
        }

        // 获取所有已报名用户
        List<ActivityRegistration> registrations = registrationMapper.selectList(
                new LambdaQueryWrapper<ActivityRegistration>()
                        .eq(ActivityRegistration::getActivityId, activityId)
                        .eq(ActivityRegistration::getIsWaiting, 0));

        // 创建提醒记录并发送提醒
        for (ActivityRegistration registration : registrations) {
            User user = userMapper.selectById(registration.getUserId());
            if (user == null) {
                continue;
            }

            // 创建提醒记录
            ActivityCheckInReminder reminder = new ActivityCheckInReminder();
            reminder.setActivityId(activityId);
            reminder.setUserId(user.getId());
            reminder.setReminderType(1);
            reminder.setContent(String.format("您报名的活动【%s】即将开始，请准时参加。活动时间：%s，地点：%s",
                    activity.getName(), activity.getStartTime(), activity.getLocation()));
            reminder.setReminderMethod(1); // 默认使用邮件通知
            reminder.setStatus(0);
            save(reminder);

            try {
                // 发送提醒
                reminder.setStatus(1);
                reminder.setSendTime(LocalDateTime.now());
            } catch (Exception e) {
                log.error("发送活动开始提醒失败", e);
                reminder.setStatus(2);
            }
            updateById(reminder);
        }

        return activityId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long sendNotCheckedInReminder(Long activityId) {
        // 获取活动信息
        Activity activity = activityMapper.selectById(activityId);
        if (activity == null) {
            throw new RuntimeException("活动不存在");
        }

        // 获取所有未签到用户
        List<ActivityRegistration> registrations = registrationMapper.selectList(
                new LambdaQueryWrapper<ActivityRegistration>()
                        .eq(ActivityRegistration::getActivityId, activityId)
                        .eq(ActivityRegistration::getIsWaiting, 0)
                        .eq(ActivityRegistration::getCheckInStatus, 0));

        // 创建提醒记录并发送提醒
        for (ActivityRegistration registration : registrations) {
            User user = userMapper.selectById(registration.getUserId());
            if (user == null) {
                continue;
            }

            // 创建提醒记录
            ActivityCheckInReminder reminder = new ActivityCheckInReminder();
            reminder.setActivityId(activityId);
            reminder.setUserId(user.getId());
            reminder.setReminderType(2);
            reminder.setContent(String.format("您报名的活动【%s】已经开始，但您还未签到。请尽快完成签到。活动时间：%s，地点：%s",
                    activity.getName(), activity.getStartTime(), activity.getLocation()));
            reminder.setReminderMethod(1); // 默认使用邮件通知
            reminder.setStatus(0);
            save(reminder);

            try {
                // 发送提醒
                reminder.setStatus(1);
                reminder.setSendTime(LocalDateTime.now());
            } catch (Exception e) {
                log.error("发送未签到提醒失败", e);
                reminder.setStatus(2);
            }
            updateById(reminder);
        }

        return activityId;
    }

    @Override
    public List<ActivityCheckInReminder> getActivityReminders(Long activityId) {
        return list(new LambdaQueryWrapper<ActivityCheckInReminder>()
                .eq(ActivityCheckInReminder::getActivityId, activityId)
                .orderByDesc(ActivityCheckInReminder::getId));
    }

    @Override
    public List<ActivityCheckInReminder> getUserReminders(Long userId) {
        return list(new LambdaQueryWrapper<ActivityCheckInReminder>()
                .eq(ActivityCheckInReminder::getUserId, userId)
                .orderByDesc(ActivityCheckInReminder::getId));
    }

    @Override
    public boolean updateReminderStatus(Long reminderId, Integer status) {
        ActivityCheckInReminder reminder = new ActivityCheckInReminder();
        reminder.setId(reminderId);
        reminder.setStatus(status);
        if (status == 1) {
            reminder.setSendTime(LocalDateTime.now());
        }
        return updateById(reminder);
    }

    @Override
    public void sendActivityStartReminder(Long activityId) {
        try {
            Activity activity = activityMapper.selectById(activityId);
            if (activity == null) {
                throw new RuntimeException("活动不存在");
            }

            List<ActivityRegistration> registrations = registrationMapper.selectList(
                    new LambdaQueryWrapper<ActivityRegistration>()
                            .eq(ActivityRegistration::getActivityId, activityId)
                            .eq(ActivityRegistration::getIsWaiting, 0));

            for (ActivityRegistration registration : registrations) {
                User user = userMapper.selectById(registration.getUserId());
                if (user == null) {
                    continue;
                }

                ActivityCheckInReminder reminder = new ActivityCheckInReminder();
                reminder.setActivityId(activityId);
                reminder.setUserId(user.getId());
                reminder.setReminderType(1);
                reminder.setContent(String.format("您报名的活动【%s】即将开始，请准时参加。活动时间：%s，地点：%s",
                        activity.getName(), activity.getStartTime(), activity.getLocation()));
                reminder.setReminderMethod(1);
                reminder.setStatus(1);
                reminder.setSendTime(LocalDateTime.now());
                save(reminder);
            }
        } catch (Exception e) {
            log.error("发送活动开始提醒失败 - 活动ID: {}", activityId, e);
        }
    }

    @Override
    public void sendActivityEndReminder(Long activityId) {
        try {
            Activity activity = activityMapper.selectById(activityId);
            if (activity == null) {
                throw new RuntimeException("活动不存在");
            }

            List<ActivityRegistration> registrations = registrationMapper.selectList(
                    new LambdaQueryWrapper<ActivityRegistration>()
                            .eq(ActivityRegistration::getActivityId, activityId)
                            .eq(ActivityRegistration::getIsWaiting, 0)
                            .eq(ActivityRegistration::getCheckInStatus, 0));

            for (ActivityRegistration registration : registrations) {
                User user = userMapper.selectById(registration.getUserId());
                if (user == null) {
                    continue;
                }

                ActivityCheckInReminder reminder = new ActivityCheckInReminder();
                reminder.setActivityId(activityId);
                reminder.setUserId(user.getId());
                reminder.setReminderType(2);
                reminder.setContent(String.format("您报名的活动【%s】即将结束，请尽快完成签到。活动时间：%s，地点：%s",
                        activity.getName(), activity.getEndTime(), activity.getLocation()));
                reminder.setReminderMethod(1);
                reminder.setStatus(1);
                reminder.setSendTime(LocalDateTime.now());
                save(reminder);
            }
        } catch (Exception e) {
            log.error("发送活动结束提醒失败 - 活动ID: {}", activityId, e);
        }
    }

    /**
     * 定时检查活动开始提醒
     */
    @Override
    @Scheduled(cron = "0 0 8 * * ?") // 每天早上8点执行
    public void checkActivityStartReminder() {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime reminderTime = now.plusHours(1); // 提前1小时提醒

            List<Activity> activities = activityMapper.selectList(
                    new LambdaQueryWrapper<Activity>()
                            .between(Activity::getStartTime, now, reminderTime));
            for (Activity activity : activities) {
                sendActivityStartReminder(activity.getId());
            }
        } catch (Exception e) {
            log.error("检查活动开始提醒失败", e);
        }
    }

    /**
     * 定时检查活动结束提醒
     */
    @Override
    @Scheduled(cron = "0 0 8 * * ?") // 每天早上8点执行
    public void checkActivityEndReminder() {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime reminderTime = now.plusHours(1); // 提前1小时提醒

            List<Activity> activities = activityMapper.selectList(
                    new LambdaQueryWrapper<Activity>()
                            .between(Activity::getEndTime, now, reminderTime));
            for (Activity activity : activities) {
                sendActivityEndReminder(activity.getId());
            }
        } catch (Exception e) {
            log.error("检查活动结束提醒失败", e);
        }
    }

    /**
     * 分页查询签到提醒列表
     */
    @Override
    public IPage<ActivityCheckInReminderDTO> getReminderPage(Integer pageNum, Integer pageSize, Long activityId,
            Long userId, Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<ActivityCheckInReminder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(activityId != null, ActivityCheckInReminder::getActivityId, activityId)
                .eq(userId != null, ActivityCheckInReminder::getUserId, userId)
                .eq(status != null, ActivityCheckInReminder::getStatus, status)
                .orderByDesc(ActivityCheckInReminder::getId);

        // 执行分页查询
        Page<ActivityCheckInReminder> page = new Page<>(pageNum, pageSize);
        Page<ActivityCheckInReminder> reminderPage = page(page, queryWrapper);

        // 转换为DTO
        Page<ActivityCheckInReminderDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(reminderPage, dtoPage, "records");

        // 缓存活动和用户信息，避免重复查询
        Map<Long, Activity> activityMap = new HashMap<>();
        Map<Long, User> userMap = new HashMap<>();

        List<ActivityCheckInReminderDTO> dtoList = new ArrayList<>();
        for (ActivityCheckInReminder reminder : reminderPage.getRecords()) {
            ActivityCheckInReminderDTO dto = convertToDTO(reminder, activityMap, userMap);
            dtoList.add(dto);
        }

        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    /**
     * 获取签到提醒列表
     */
    @Override
    public List<ActivityCheckInReminderDTO> getReminderList(Long activityId, Long userId, Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<ActivityCheckInReminder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(activityId != null, ActivityCheckInReminder::getActivityId, activityId)
                .eq(userId != null, ActivityCheckInReminder::getUserId, userId)
                .eq(status != null, ActivityCheckInReminder::getStatus, status)
                .orderByDesc(ActivityCheckInReminder::getId);

        // 执行查询
        List<ActivityCheckInReminder> reminderList = list(queryWrapper);

        // 转换为DTO
        List<ActivityCheckInReminderDTO> dtoList = new ArrayList<>();
        Map<Long, Activity> activityMap = new HashMap<>();
        Map<Long, User> userMap = new HashMap<>();

        for (ActivityCheckInReminder reminder : reminderList) {
            ActivityCheckInReminderDTO dto = convertToDTO(reminder, activityMap, userMap);
            dtoList.add(dto);
        }

        return dtoList;
    }

    /**
     * 获取签到提醒详情
     */
    @Override
    public ActivityCheckInReminderDTO getReminderDetail(Long id) {
        // 获取提醒记录
        ActivityCheckInReminder reminder = getById(id);
        if (reminder == null) {
            return null;
        }

        // 转换为DTO
        Map<Long, Activity> activityMap = new HashMap<>();
        Map<Long, User> userMap = new HashMap<>();
        return convertToDTO(reminder, activityMap, userMap);
    }

    /**
     * 创建签到提醒
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createReminder(ActivityCheckInReminderDTO reminderDTO) {
        // 检查活动是否存在
        Activity activity = activityMapper.selectById(reminderDTO.getActivityId());
        if (activity == null) {
            throw new RuntimeException("活动不存在");
        }

        // 检查用户是否存在
        User user = userMapper.selectById(reminderDTO.getUserId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 创建提醒记录
        ActivityCheckInReminder reminder = new ActivityCheckInReminder();
        BeanUtils.copyProperties(reminderDTO, reminder);

        // 如果状态为已发送，设置发送时间
        if (reminder.getStatus() != null && reminder.getStatus() == 1) {
            reminder.setSendTime(LocalDateTime.now());
        }

        save(reminder);
        return reminder.getId();
    }

    /**
     * 更新签到提醒
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateReminder(ActivityCheckInReminderDTO reminderDTO) {
        // 检查提醒记录是否存在
        ActivityCheckInReminder reminder = getById(reminderDTO.getId());
        if (reminder == null) {
            throw new RuntimeException("提醒记录不存在");
        }

        // 更新提醒记录
        BeanUtils.copyProperties(reminderDTO, reminder);

        // 如果状态为已发送且之前未发送，设置发送时间
        if (reminder.getStatus() != null && reminder.getStatus() == 1 && reminder.getSendTime() == null) {
            reminder.setSendTime(LocalDateTime.now());
        }

        return updateById(reminder);
    }

    /**
     * 删除签到提醒
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteReminder(Long id) {
        // 检查提醒记录是否存在
        ActivityCheckInReminder reminder = getById(id);
        if (reminder == null) {
            throw new RuntimeException("提醒记录不存在");
        }

        // 删除提醒记录
        return removeById(id);
    }

    /**
     * 将实体转换为DTO
     */
    private ActivityCheckInReminderDTO convertToDTO(ActivityCheckInReminder reminder, Map<Long, Activity> activityMap,
            Map<Long, User> userMap) {
        ActivityCheckInReminderDTO dto = new ActivityCheckInReminderDTO();
        BeanUtils.copyProperties(reminder, dto);

        // 设置活动名称
        if (reminder.getActivityId() != null) {
            Activity activity = activityMap.computeIfAbsent(reminder.getActivityId(),
                    id -> activityMapper.selectById(id));
            if (activity != null) {
                dto.setActivityName(activity.getName());
            }
        }

        // 设置用户名称
        if (reminder.getUserId() != null) {
            User user = userMap.computeIfAbsent(reminder.getUserId(),
                    id -> userMapper.selectById(id));
            if (user != null) {
                dto.setUserName(user.getUsername());
            }
        }

        // 设置提醒方式名称
        if (reminder.getReminderMethod() != null) {
            switch (reminder.getReminderMethod()) {
                case 1:
                    dto.setReminderTypeName("邮件");
                    break;
                case 2:
                    dto.setReminderTypeName("短信");
                    break;
                case 3:
                    dto.setReminderTypeName("站内信");
                    break;
                default:
                    dto.setReminderTypeName("未知");
            }
        }

        // 设置状态名称
        if (reminder.getStatus() != null) {
            switch (reminder.getStatus()) {
                case 0:
                    dto.setStatusName("未发送");
                    break;
                case 1:
                    dto.setStatusName("已发送");
                    break;
                case 2:
                    dto.setStatusName("发送失败");
                    break;
                default:
                    dto.setStatusName("未知");
            }
        }

        return dto;
    }
}