package com.stadium.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stadium.dto.ActivityNotificationDTO;
import com.stadium.entity.Activity;
import com.stadium.entity.ActivityNotification;
import com.stadium.entity.User;
import com.stadium.mapper.ActivityMapper;
import com.stadium.mapper.ActivityNotificationMapper;
import com.stadium.mapper.UserMapper;
import com.stadium.service.ActivityNotificationService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动通知服务实现类
 */
@Service
public class ActivityNotificationServiceImpl implements ActivityNotificationService {

    @Autowired
    private ActivityNotificationMapper notificationMapper;

    @Autowired
    private ActivityMapper activityMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    @Transactional
    public boolean sendNotification(ActivityNotification notification) {
        notification.setIsRead(0);
        return notificationMapper.insert(notification) > 0;
    }

    @Override
    @Transactional
    public boolean markAsRead(Long notificationId) {
        ActivityNotification notification = new ActivityNotification();
        notification.setId(notificationId);
        notification.setIsRead(1);
        return notificationMapper.updateById(notification) > 0;
    }

    @Override
    public Page<ActivityNotification> getUserNotifications(Long userId, Page<ActivityNotification> page) {
        LambdaQueryWrapper<ActivityNotification> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityNotification::getUserId, userId)
                .orderByDesc(ActivityNotification::getId);
        return notificationMapper.selectPage(page, wrapper);
    }

    @Override
    public int getUnreadCount(Long userId) {
        LambdaQueryWrapper<ActivityNotification> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityNotification::getUserId, userId)
                .eq(ActivityNotification::getIsRead, 0);
        return Math.toIntExact(notificationMapper.selectCount(wrapper));
    }

    @Override
    public boolean sendStatusChangeNotification(Long activityId, Long userId, String title, String content) {
        ActivityNotification notification = new ActivityNotification();
        notification.setActivityId(activityId);
        notification.setUserId(userId);
        notification.setType(1);
        notification.setTitle(title);
        notification.setContent(content);
        return sendNotification(notification);
    }

    @Override
    public boolean sendRegistrationSuccessNotification(Long activityId, Long userId) {
        Activity activity = activityMapper.selectById(activityId);
        if (activity == null) {
            return false;
        }

        ActivityNotification notification = new ActivityNotification();
        notification.setActivityId(activityId);
        notification.setUserId(userId);
        notification.setType(2);
        notification.setTitle("报名成功通知");
        notification.setContent(String.format("您已成功报名活动【%s】，活动将于%s开始，请准时参加。",
                activity.getName(), activity.getStartTime()));
        return sendNotification(notification);
    }

    @Override
    public boolean sendActivityStartReminder(Long activityId, Long userId) {
        Activity activity = activityMapper.selectById(activityId);
        if (activity == null) {
            return false;
        }

        ActivityNotification notification = new ActivityNotification();
        notification.setActivityId(activityId);
        notification.setUserId(userId);
        notification.setType(3);
        notification.setTitle("活动即将开始提醒");
        notification.setContent(String.format("您报名的活动【%s】将于%s开始，请准时参加。",
                activity.getName(), activity.getStartTime()));
        return sendNotification(notification);
    }

    @Override
    public boolean sendReviewReminder(Long activityId, Long userId) {
        Activity activity = activityMapper.selectById(activityId);
        if (activity == null) {
            return false;
        }

        ActivityNotification notification = new ActivityNotification();
        notification.setActivityId(activityId);
        notification.setUserId(userId);
        notification.setType(4);
        notification.setTitle("活动评价提醒");
        notification.setContent(String.format("您参加的活动【%s】已结束，请对活动进行评价。",
                activity.getName()));
        return sendNotification(notification);
    }

    @Override
    public IPage<ActivityNotificationDTO> getNotificationPage(Integer pageNum, Integer pageSize, Long activityId,
            Long userId, Integer status) {
        // 创建分页对象
        Page<ActivityNotification> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<ActivityNotification> queryWrapper = new LambdaQueryWrapper<>();
        if (activityId != null) {
            queryWrapper.eq(ActivityNotification::getActivityId, activityId);
        }
        if (userId != null) {
            queryWrapper.eq(ActivityNotification::getUserId, userId);
        }
        if (status != null) {
            queryWrapper.eq(ActivityNotification::getIsRead, status);
        }
        queryWrapper.orderByDesc(ActivityNotification::getId);

        // 执行分页查询
        Page<ActivityNotification> notificationPage = notificationMapper.selectPage(page, queryWrapper);

        // 转换为DTO
        Page<ActivityNotificationDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(notificationPage, dtoPage, "records");

        // 获取活动和用户信息
        List<ActivityNotificationDTO> dtoList = new ArrayList<>();
        Map<Long, Activity> activityMap = new HashMap<>();
        Map<Long, User> userMap = new HashMap<>();

        for (ActivityNotification notification : notificationPage.getRecords()) {
            ActivityNotificationDTO dto = convertToDTO(notification, activityMap, userMap);
            dtoList.add(dto);
        }

        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public List<ActivityNotificationDTO> getNotificationList(Long activityId, Long userId, Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<ActivityNotification> queryWrapper = new LambdaQueryWrapper<>();
        if (activityId != null) {
            queryWrapper.eq(ActivityNotification::getActivityId, activityId);
        }
        if (userId != null) {
            queryWrapper.eq(ActivityNotification::getUserId, userId);
        }
        if (status != null) {
            queryWrapper.eq(ActivityNotification::getIsRead, status);
        }
        queryWrapper.orderByDesc(ActivityNotification::getId);

        // 执行查询
        List<ActivityNotification> notificationList = notificationMapper.selectList(queryWrapper);

        // 转换为DTO
        List<ActivityNotificationDTO> dtoList = new ArrayList<>();
        Map<Long, Activity> activityMap = new HashMap<>();
        Map<Long, User> userMap = new HashMap<>();

        for (ActivityNotification notification : notificationList) {
            ActivityNotificationDTO dto = convertToDTO(notification, activityMap, userMap);
            dtoList.add(dto);
        }

        return dtoList;
    }

    @Override
    public ActivityNotificationDTO getNotificationDetail(Long id) {
        // 查询通知
        ActivityNotification notification = notificationMapper.selectById(id);
        if (notification == null) {
            return null;
        }

        // 转换为DTO
        Map<Long, Activity> activityMap = new HashMap<>();
        Map<Long, User> userMap = new HashMap<>();
        return convertToDTO(notification, activityMap, userMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createNotification(ActivityNotificationDTO notificationDTO) {
        // 验证参数
        if (notificationDTO.getActivityId() == null || notificationDTO.getUserId() == null) {
            throw new RuntimeException("活动ID和用户ID不能为空");
        }

        // 检查活动是否存在
        Activity activity = activityMapper.selectById(notificationDTO.getActivityId());
        if (activity == null) {
            throw new RuntimeException("活动不存在");
        }

        // 检查用户是否存在
        User user = userMapper.selectById(notificationDTO.getUserId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 创建通知
        ActivityNotification notification = new ActivityNotification();
        notification.setActivityId(notificationDTO.getActivityId());
        notification.setUserId(notificationDTO.getUserId());
        notification.setType(notificationDTO.getNotificationType());
        notification.setTitle(notificationDTO.getTitle());
        notification.setContent(notificationDTO.getContent());
        notification.setIsRead(0); // 默认未读

        // 保存通知
        notificationMapper.insert(notification);

        return notification.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotification(ActivityNotificationDTO notificationDTO) {
        // 验证参数
        if (notificationDTO.getId() == null) {
            throw new RuntimeException("通知ID不能为空");
        }

        // 查询通知
        ActivityNotification notification = notificationMapper.selectById(notificationDTO.getId());
        if (notification == null) {
            throw new RuntimeException("通知不存在");
        }

        // 更新通知
        notification.setTitle(notificationDTO.getTitle());
        notification.setContent(notificationDTO.getContent());
        notification.setType(notificationDTO.getNotificationType());

        return notificationMapper.updateById(notification) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNotification(Long id) {
        // 查询通知
        ActivityNotification notification = notificationMapper.selectById(id);
        if (notification == null) {
            throw new RuntimeException("通知不存在");
        }

        // 删除通知
        return notificationMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotificationStatus(Long id, Integer status) {
        // 查询通知
        ActivityNotification notification = notificationMapper.selectById(id);
        if (notification == null) {
            throw new RuntimeException("通知不存在");
        }

        // 更新状态
        notification.setIsRead(status);

        return notificationMapper.updateById(notification) > 0;
    }

    /**
     * 将实体转换为DTO
     */
    private ActivityNotificationDTO convertToDTO(ActivityNotification notification, Map<Long, Activity> activityMap,
            Map<Long, User> userMap) {
        ActivityNotificationDTO dto = new ActivityNotificationDTO();
        BeanUtils.copyProperties(notification, dto);

        // 设置通知类型
        dto.setNotificationType(notification.getType());

        // 设置状态
        dto.setStatus(notification.getIsRead());

        // 设置活动名称
        if (notification.getActivityId() != null) {
            Activity activity = activityMap.computeIfAbsent(notification.getActivityId(),
                    id -> activityMapper.selectById(id));
            if (activity != null) {
                dto.setActivityName(activity.getName());
            }
        }

        // 设置用户名称
        if (notification.getUserId() != null) {
            User user = userMap.computeIfAbsent(notification.getUserId(),
                    id -> userMapper.selectById(id));
            if (user != null) {
                dto.setUserName(user.getUsername());
            }
        }

        // 设置通知类型名称
        if (notification.getType() != null) {
            switch (notification.getType()) {
                case 1:
                    dto.setNotificationTypeName("活动状态变更");
                    break;
                case 2:
                    dto.setNotificationTypeName("报名成功");
                    break;
                case 3:
                    dto.setNotificationTypeName("活动开始提醒");
                    break;
                case 4:
                    dto.setNotificationTypeName("评价提醒");
                    break;
                default:
                    dto.setNotificationTypeName("其他通知");
            }
        }

        // 设置状态名称
        if (notification.getIsRead() != null) {
            dto.setStatusName(notification.getIsRead() == 0 ? "未读" : "已读");
        }

        return dto;
    }
}