package com.stadium.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stadium.dto.ActivityRefundDTO;
import com.stadium.entity.Activity;
import com.stadium.entity.ActivityRefund;
import com.stadium.entity.ActivityRegistration;
import com.stadium.entity.User;
import com.stadium.mapper.ActivityMapper;
import com.stadium.mapper.ActivityRefundMapper;
import com.stadium.mapper.ActivityRegistrationMapper;
import com.stadium.mapper.UserMapper;
import com.stadium.service.ActivityRefundService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 活动退款服务实现类
 */
@Service
public class ActivityRefundServiceImpl implements ActivityRefundService {

    @Autowired
    private ActivityRefundMapper refundMapper;

    @Autowired
    private ActivityRegistrationMapper registrationMapper;

    @Autowired
    private ActivityMapper activityMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    @Transactional
    public Long applyRefund(ActivityRefund refund) {
        // 检查报名记录是否存在且已支付
        ActivityRegistration registration = registrationMapper.selectById(refund.getRegistrationId());
        if (registration == null) {
            throw new RuntimeException("报名记录不存在");
        }
        if (registration.getPaymentStatus() != 1) {
            throw new RuntimeException("未支付，不能申请退款");
        }

        // 设置初始状态为待审核
        refund.setStatus(ActivityRefund.STATUS_PENDING);
        refundMapper.insert(refund);
        return refund.getId();
    }

    @Override
    @Transactional
    public void reviewRefund(Long refundId, Integer status, String reviewRemark, Long reviewerId) {
        ActivityRefund refund = refundMapper.selectById(refundId);
        if (refund == null) {
            throw new RuntimeException("退款记录不存在");
        }
        if (refund.getStatus() != ActivityRefund.STATUS_PENDING) {
            throw new RuntimeException("退款状态不正确");
        }

        refund.setStatus(status);
        refund.setReviewRemark(reviewRemark);
        refund.setReviewerId(reviewerId);
        refund.setReviewTime(LocalDateTime.now());
        refundMapper.updateById(refund);
    }

    @Override
    @Transactional
    public void processRefund(Long refundId) {
        ActivityRefund refund = refundMapper.selectById(refundId);
        if (refund == null) {
            throw new RuntimeException("退款记录不存在");
        }
        if (refund.getStatus() != ActivityRefund.STATUS_APPROVED) {
            throw new RuntimeException("退款未通过审核");
        }

        // 更新退款状态为已退款
        refund.setStatus(ActivityRefund.STATUS_REFUNDED);
        refund.setRefundTime(LocalDateTime.now());
        refundMapper.updateById(refund);

        // 更新报名状态为已退款
        ActivityRegistration registration = registrationMapper.selectById(refund.getRegistrationId());
        registration.setStatus(4); // 已退款
        registrationMapper.updateById(registration);
    }

    @Override
    public Page<ActivityRefund> getUserRefunds(Long userId, Page<ActivityRefund> page) {
        LambdaQueryWrapper<ActivityRefund> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityRefund::getUserId, userId)
                .orderByDesc(ActivityRefund::getId);
        return refundMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<ActivityRefund> getActivityRefunds(Long activityId, Page<ActivityRefund> page) {
        LambdaQueryWrapper<ActivityRefund> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityRefund::getActivityId, activityId)
                .orderByDesc(ActivityRefund::getId);
        return refundMapper.selectPage(page, wrapper);
    }

    @Override
    public Page<ActivityRefund> getPendingRefunds(Page<ActivityRefund> page) {
        LambdaQueryWrapper<ActivityRefund> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityRefund::getStatus, ActivityRefund.STATUS_PENDING)
                .orderByAsc(ActivityRefund::getId);
        return refundMapper.selectPage(page, wrapper);
    }

    @Override
    public ActivityRefund getRefundEntityDetail(Long refundId) {
        return refundMapper.selectById(refundId);
    }

    @Override
    public IPage<ActivityRefundDTO> getRefundPage(Integer pageNum, Integer pageSize, Long activityId, Long userId,
            Integer status) {
        // 创建分页对象
        Page<ActivityRefund> page = new Page<>(pageNum, pageSize);

        // 构建查询条件
        LambdaQueryWrapper<ActivityRefund> queryWrapper = new LambdaQueryWrapper<>();
        if (activityId != null) {
            queryWrapper.eq(ActivityRefund::getActivityId, activityId);
        }
        if (userId != null) {
            queryWrapper.eq(ActivityRefund::getUserId, userId);
        }
        if (status != null) {
            queryWrapper.eq(ActivityRefund::getStatus, status);
        }
        queryWrapper.orderByDesc(ActivityRefund::getId);

        // 执行分页查询
        Page<ActivityRefund> refundPage = refundMapper.selectPage(page, queryWrapper);

        // 转换为DTO
        Page<ActivityRefundDTO> dtoPage = new Page<>();
        BeanUtils.copyProperties(refundPage, dtoPage, "records");

        // 获取活动和用户信息
        List<ActivityRefundDTO> dtoList = new ArrayList<>();
        Map<Long, Activity> activityMap = new HashMap<>();
        Map<Long, User> userMap = new HashMap<>();

        for (ActivityRefund refund : refundPage.getRecords()) {
            ActivityRefundDTO dto = convertToDTO(refund, activityMap, userMap);
            dtoList.add(dto);
        }

        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public List<ActivityRefundDTO> getRefundList(Long activityId, Long userId, Integer status) {
        // 构建查询条件
        LambdaQueryWrapper<ActivityRefund> queryWrapper = new LambdaQueryWrapper<>();
        if (activityId != null) {
            queryWrapper.eq(ActivityRefund::getActivityId, activityId);
        }
        if (userId != null) {
            queryWrapper.eq(ActivityRefund::getUserId, userId);
        }
        if (status != null) {
            queryWrapper.eq(ActivityRefund::getStatus, status);
        }
        queryWrapper.orderByDesc(ActivityRefund::getId);

        // 执行查询
        List<ActivityRefund> refundList = refundMapper.selectList(queryWrapper);

        // 转换为DTO
        List<ActivityRefundDTO> dtoList = new ArrayList<>();
        Map<Long, Activity> activityMap = new HashMap<>();
        Map<Long, User> userMap = new HashMap<>();

        for (ActivityRefund refund : refundList) {
            ActivityRefundDTO dto = convertToDTO(refund, activityMap, userMap);
            dtoList.add(dto);
        }

        return dtoList;
    }

    @Override
    public ActivityRefundDTO getRefundDetail(Long id) {
        // 查询退款
        ActivityRefund refund = refundMapper.selectById(id);
        if (refund == null) {
            return null;
        }

        // 转换为DTO
        Map<Long, Activity> activityMap = new HashMap<>();
        Map<Long, User> userMap = new HashMap<>();
        return convertToDTO(refund, activityMap, userMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createRefund(ActivityRefundDTO refundDTO) {
        // 验证参数
        if (refundDTO.getActivityId() == null || refundDTO.getUserId() == null || refundDTO.getAmount() == null) {
            throw new RuntimeException("活动ID、用户ID和退款金额不能为空");
        }

        // 检查活动是否存在
        Activity activity = activityMapper.selectById(refundDTO.getActivityId());
        if (activity == null) {
            throw new RuntimeException("活动不存在");
        }

        // 检查用户是否存在
        User user = userMapper.selectById(refundDTO.getUserId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 创建退款记录
        ActivityRefund refund = new ActivityRefund();
        refund.setActivityId(refundDTO.getActivityId());
        refund.setUserId(refundDTO.getUserId());
        refund.setAmount(refundDTO.getAmount());
        refund.setReason(refundDTO.getReason());
        refund.setStatus(ActivityRefund.STATUS_PENDING);

        // 保存退款记录
        refundMapper.insert(refund);

        return refund.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRefund(ActivityRefundDTO refundDTO) {
        // 验证参数
        if (refundDTO.getId() == null) {
            throw new RuntimeException("退款ID不能为空");
        }

        // 查询退款记录
        ActivityRefund refund = refundMapper.selectById(refundDTO.getId());
        if (refund == null) {
            throw new RuntimeException("退款记录不存在");
        }

        // 更新退款记录
        refund.setReason(refundDTO.getReason());

        return refundMapper.updateById(refund) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRefund(Long id) {
        // 查询退款记录
        ActivityRefund refund = refundMapper.selectById(id);
        if (refund == null) {
            throw new RuntimeException("退款记录不存在");
        }

        // 只能删除待审核的退款
        if (refund.getStatus() != ActivityRefund.STATUS_PENDING) {
            throw new RuntimeException("只能删除待审核的退款");
        }

        // 删除退款记录
        return refundMapper.deleteById(id) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateRefundStatus(Long id, Integer status) {
        // 查询退款记录
        ActivityRefund refund = refundMapper.selectById(id);
        if (refund == null) {
            throw new RuntimeException("退款记录不存在");
        }

        // 更新状态
        refund.setStatus(status);

        // 如果是已退款状态，设置退款时间
        if (status == ActivityRefund.STATUS_REFUNDED) {
            refund.setRefundTime(LocalDateTime.now());
        }

        return refundMapper.updateById(refund) > 0;
    }

    /**
     * 将实体转换为DTO
     */
    private ActivityRefundDTO convertToDTO(ActivityRefund refund, Map<Long, Activity> activityMap,
            Map<Long, User> userMap) {
        ActivityRefundDTO dto = new ActivityRefundDTO();
        BeanUtils.copyProperties(refund, dto);

        // 设置活动名称
        if (refund.getActivityId() != null) {
            Activity activity = activityMap.computeIfAbsent(refund.getActivityId(),
                    id -> activityMapper.selectById(id));
            if (activity != null) {
                dto.setActivityName(activity.getName());
            }
        }

        // 设置用户名称
        if (refund.getUserId() != null) {
            User user = userMap.computeIfAbsent(refund.getUserId(),
                    id -> userMapper.selectById(id));
            if (user != null) {
                dto.setUserName(user.getUsername());
            }
        }

        // 设置状态名称
        if (refund.getStatus() != null) {
            switch (refund.getStatus()) {
                case ActivityRefund.STATUS_PENDING:
                    dto.setStatusName("待审核");
                    break;
                case ActivityRefund.STATUS_APPROVED:
                    dto.setStatusName("已通过");
                    break;
                case ActivityRefund.STATUS_REJECTED:
                    dto.setStatusName("已拒绝");
                    break;
                case ActivityRefund.STATUS_REFUNDED:
                    dto.setStatusName("已退款");
                    break;
                default:
                    dto.setStatusName("未知");
            }
        }

        return dto;
    }

    @Override
    public Page<ActivityRefund> getApprovedRefunds(Page<ActivityRefund> page) {
        LambdaQueryWrapper<ActivityRefund> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ActivityRefund::getStatus, ActivityRefund.STATUS_APPROVED)
                .orderByDesc(ActivityRefund::getReviewTime);
        return refundMapper.selectPage(page, wrapper);
    }
}