package com.stadium.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stadium.dto.ActivityReviewDTO;
import com.stadium.entity.ActivityReview;
import com.stadium.mapper.ActivityReviewMapper;
import com.stadium.service.ActivityReviewService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 活动评价服务实现类
 */
@Service
public class ActivityReviewServiceImpl implements ActivityReviewService {

    @Autowired
    private ActivityReviewMapper reviewMapper;

    // 新接口实现
    @Override
    public IPage<ActivityReviewDTO> getReviewPage(Integer pageNum, Integer pageSize, Long activityId, Long userId) {
        Page<ActivityReview> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<ActivityReview> wrapper = new LambdaQueryWrapper<>();
        if (activityId != null)
            wrapper.eq(ActivityReview::getActivityId, activityId);
        if (userId != null)
            wrapper.eq(ActivityReview::getUserId, userId);
        wrapper.eq(ActivityReview::getStatus, 1); // 只查已通过
        wrapper.orderByDesc(ActivityReview::getId);
        IPage<ActivityReview> entityPage = reviewMapper.selectPage(page, wrapper);
        List<ActivityReviewDTO> dtoList = entityPage.getRecords().stream().map(this::toDTO)
                .collect(Collectors.toList());
        Page<ActivityReviewDTO> dtoPage = new Page<>(pageNum, pageSize, entityPage.getTotal());
        dtoPage.setRecords(dtoList);
        return dtoPage;
    }

    @Override
    public List<ActivityReviewDTO> getReviewList(Long activityId, Long userId) {
        LambdaQueryWrapper<ActivityReview> wrapper = new LambdaQueryWrapper<>();
        if (activityId != null)
            wrapper.eq(ActivityReview::getActivityId, activityId);
        if (userId != null)
            wrapper.eq(ActivityReview::getUserId, userId);
        wrapper.eq(ActivityReview::getStatus, 1);
        wrapper.orderByDesc(ActivityReview::getId);
        List<ActivityReview> list = reviewMapper.selectList(wrapper);
        return list.stream().map(this::toDTO).collect(Collectors.toList());
    }

    @Override
    public ActivityReviewDTO getReviewDetail(Long id) {
        ActivityReview review = reviewMapper.selectById(id);
        return review != null ? toDTO(review) : null;
    }

    @Override
    @Transactional
    public Long createReview(ActivityReviewDTO reviewDTO) {
        ActivityReview review = new ActivityReview();
        BeanUtils.copyProperties(reviewDTO, review);
        review.setStatus(1); // 默认通过
        review.setLikeCount(0);
        reviewMapper.insert(review);
        return review.getId();
    }

    @Override
    @Transactional
    public void updateReview(ActivityReviewDTO reviewDTO) {
        ActivityReview review = reviewMapper.selectById(reviewDTO.getId());
        if (review != null) {
            BeanUtils.copyProperties(reviewDTO, review, "id", "createTime");
            reviewMapper.updateById(review);
        }
    }

    @Override
    @Transactional
    public void deleteReview(Long id) {
        reviewMapper.deleteById(id);
    }

    // =================== 以下为旧接口实现，已不再需要 ===================
    // @Override
    // @Transactional
    // public Long submitReview(ActivityReview review) { ... }
    // @Override
    // @Transactional
    // public void reviewReview(Long reviewId, Integer status, String remark, Long
    // reviewerId) { ... }
    // @Override
    // @Transactional
    // public void replyReview(Long reviewId, String reply, Long replyUserId) { ...
    // }
    // @Override
    // public Page<ActivityReview> getActivityReviews(Long activityId,
    // Page<ActivityReview> page) { ... }
    // @Override
    // public Page<ActivityReview> getUserReviews(Long userId, Page<ActivityReview>
    // page) { ... }
    // @Override
    // public Page<ActivityReview> getPendingReviews(Page<ActivityReview> page) {
    // ... }
    // @Override
    // public ActivityReview getReviewDetail(Long reviewId) { ... }
    // @Override
    // @Transactional
    // public void likeReview(Long reviewId) { ... }
    // @Override
    // public Double getActivityAverageRating(Long activityId) { ... }

    // 实体转DTO
    private ActivityReviewDTO toDTO(ActivityReview review) {
        ActivityReviewDTO dto = new ActivityReviewDTO();
        BeanUtils.copyProperties(review, dto);
        return dto;
    }
}