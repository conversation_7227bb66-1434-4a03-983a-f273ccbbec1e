package com.stadium.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.stadium.dto.ActivityCheckInReminderDTO;
import com.stadium.entity.Activity;
import com.stadium.entity.ActivityCheckInReminder;
import com.stadium.entity.ActivityRegistration;
import com.stadium.entity.User;
import com.stadium.mapper.ActivityCheckInReminderMapper;
import com.stadium.mapper.ActivityMapper;
import com.stadium.mapper.ActivityRegistrationMapper;
import com.stadium.mapper.UserMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ActivityCheckInReminderServiceImpl 单元测试
 */
public class ActivityCheckInReminderServiceImplTest {

    @Mock
    private ActivityCheckInReminderMapper reminderMapper;

    @Mock
    private ActivityMapper activityMapper;

    @Mock
    private ActivityRegistrationMapper registrationMapper;

    @Mock
    private UserMapper userMapper;

    @InjectMocks
    private ActivityCheckInReminderServiceImpl reminderService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testGetReminderPage() {
        // 准备测试数据
        Integer pageNum = 1;
        Integer pageSize = 10;
        Long activityId = 1L;
        Long userId = 1L;
        Integer status = 0;

        // 模拟数据库查询结果
        List<ActivityCheckInReminder> reminderList = new ArrayList<>();
        ActivityCheckInReminder reminder = new ActivityCheckInReminder();
        reminder.setId(1L);
        reminder.setActivityId(activityId);
        reminder.setUserId(userId);
        reminder.setContent("测试提醒内容");
        reminder.setReminderMethod(1);
        reminder.setStatus(status);
        // createTime 现在由 MyBatis-Plus 自动填充，不需要手动设置
        reminderList.add(reminder);

        Page<ActivityCheckInReminder> page = new Page<>(pageNum, pageSize);
        page.setRecords(reminderList);
        page.setTotal(1);

        // 模拟活动和用户数据
        Activity activity = new Activity();
        activity.setId(activityId);
        activity.setName("测试活动");

        User user = new User();
        user.setId(userId);
        user.setUsername("testuser");

        // 设置模拟行为
        when(reminderMapper.selectPage(any(Page.class), any(LambdaQueryWrapper.class)))
                .thenReturn((Page<ActivityCheckInReminder>) page);
        when(activityMapper.selectById(activityId)).thenReturn(activity);
        when(userMapper.selectById(userId)).thenReturn(user);

        // 执行测试
        IPage<ActivityCheckInReminderDTO> result = reminderService.getReminderPage(pageNum, pageSize, activityId,
                userId, status);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRecords().size());
        assertEquals(activityId, result.getRecords().get(0).getActivityId());
        assertEquals(userId, result.getRecords().get(0).getUserId());
        assertEquals("测试活动", result.getRecords().get(0).getActivityName());
        assertEquals("testuser", result.getRecords().get(0).getUserName());

        // 验证方法调用
        verify(reminderMapper).selectPage(any(Page.class), any(LambdaQueryWrapper.class));
        verify(activityMapper).selectById(activityId);
        verify(userMapper).selectById(userId);
    }

    @Test
    public void testCreateReminder() {
        // 准备测试数据
        ActivityCheckInReminderDTO reminderDTO = new ActivityCheckInReminderDTO();
        reminderDTO.setActivityId(1L);
        reminderDTO.setUserId(1L);
        reminderDTO.setContent("测试提醒内容");
        reminderDTO.setReminderMethod(1);
        reminderDTO.setStatus(0);

        // 模拟活动和用户数据
        Activity activity = new Activity();
        activity.setId(1L);
        activity.setName("测试活动");

        User user = new User();
        user.setId(1L);
        user.setUsername("testuser");

        // 设置模拟行为
        when(activityMapper.selectById(1L)).thenReturn(activity);
        when(userMapper.selectById(1L)).thenReturn(user);
        when(reminderMapper.insert(any(ActivityCheckInReminder.class))).thenReturn(1);

        // 执行测试
        Long result = reminderService.createReminder(reminderDTO);

        // 验证结果
        assertNotNull(result);

        // 验证方法调用
        verify(activityMapper).selectById(1L);
        verify(userMapper).selectById(1L);
        verify(reminderMapper).insert(any(ActivityCheckInReminder.class));
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testSendActivityStartReminder() {
        // 准备测试数据
        Long activityId = 1L;

        // 模拟活动数据
        Activity activity = new Activity();
        activity.setId(activityId);
        activity.setName("测试活动");
        activity.setStartTime(LocalDateTime.now().plusHours(1));
        activity.setLocation("测试场地");

        // 模拟报名数据
        List<ActivityRegistration> registrations = new ArrayList<>();
        ActivityRegistration registration = new ActivityRegistration();
        registration.setActivityId(activityId);
        registration.setUserId(1L);
        registration.setIsWaiting(0);
        registrations.add(registration);

        // 模拟用户数据
        User user = new User();
        user.setId(1L);
        user.setUsername("testuser");

        // 设置模拟行为
        when(activityMapper.selectById(activityId)).thenReturn(activity);
        when(registrationMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(registrations);
        when(userMapper.selectById(1L)).thenReturn(user);
        when(reminderMapper.insert(any(ActivityCheckInReminder.class))).thenReturn(1);

        // 执行测试
        reminderService.sendActivityStartReminder(activityId);

        // 验证方法调用
        verify(activityMapper).selectById(activityId);
        verify(registrationMapper).selectList(any(LambdaQueryWrapper.class));
        verify(userMapper).selectById(1L);
        verify(reminderMapper).insert(any(ActivityCheckInReminder.class));
    }
}
