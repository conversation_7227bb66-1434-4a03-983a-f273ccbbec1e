@echo off
chcp 65001
echo ========================================
echo 修复服务类中的时间字段设置问题
echo ========================================
echo.

echo 正在移除手动设置createTime和updateTime的代码...

REM 移除setCreateTime调用
powershell -Command "(Get-ChildItem -Path 'backend\src\main\java\com\stadium\service\impl' -Filter '*.java' -Recurse) | ForEach-Object { (Get-Content $_.FullName) -replace '.*\.setCreateTime\(.*\);?', '' | Set-Content $_.FullName }"

REM 移除setUpdateTime调用
powershell -Command "(Get-ChildItem -Path 'backend\src\main\java\com\stadium\service\impl' -Filter '*.java' -Recurse) | ForEach-Object { (Get-Content $_.FullName) -replace '.*\.setUpdateTime\(.*\);?', '' | Set-Content $_.FullName }"

REM 替换getCreateTime方法引用为getId（作为替代排序字段）
powershell -Command "(Get-ChildItem -Path 'backend\src\main\java\com\stadium\service\impl' -Filter '*.java' -Recurse) | ForEach-Object { (Get-Content $_.FullName) -replace '::getCreateTime', '::getId' | Set-Content $_.FullName }"

echo.
echo 修复完成！
echo.
pause
